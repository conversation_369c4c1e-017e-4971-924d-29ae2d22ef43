#!/usr/bin/env python3
"""
Cross-Category Indents Validation Test Script

This script helps verify the accuracy of cross-category indent calculations
by providing detailed analysis and validation of the numbers.

Usage:
    python test_cross_category_indents.py --tenant-id <tenant_id> --start-date <YYYY-MM-DD> --end-date <YYYY-MM-DD>
"""

import sys
import os
import argparse
import pandas as pd
import json
from datetime import datetime, timedelta
import logging

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from utility.dashboard_agents import create_reconciliation_table_data, format_indian_currency

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cross_category_test.log'),
        logging.StreamHandler()
    ]
)

def create_sample_data():
    """Create sample data for testing cross-category indents"""
    
    # Sample consumption data with cross-category indents
    consumption_data = [
        # Category A items being indented to Category B's workarea
        {'Category': 'BEVERAGES', 'Sub Category': 'Tea', 'WorkArea': 'Kitchen_Main', 
         'WorkArea Indent': 10, 'WAC(incl.tax,etc)': 50.0},
        {'Category': 'BEVERAGES', 'Sub Category': 'Coffee', 'WorkArea': 'Kitchen_Main', 
         'WorkArea Indent': 5, 'WAC(incl.tax,etc)': 80.0},
        
        # Category B items in their own workarea
        {'Category': 'FOOD', 'Sub Category': 'Rice', 'WorkArea': 'Kitchen_Main', 
         'WorkArea Indent': 20, 'WAC(incl.tax,etc)': 30.0},
        {'Category': 'FOOD', 'Sub Category': 'Dal', 'WorkArea': 'Kitchen_Main', 
         'WorkArea Indent': 15, 'WAC(incl.tax,etc)': 40.0},
         
        # Category C items being indented to Category A's workarea  
        {'Category': 'SNACKS', 'Sub Category': 'Chips', 'WorkArea': 'Bar_Counter', 
         'WorkArea Indent': 8, 'WAC(incl.tax,etc)': 25.0},
    ]
    
    # Sample store data
    store_data = [
        {'Category': 'BEVERAGES', 'Sub Category': 'Tea', 'Opening Qty': 100, 'Closing Qty': 80, 'WAC(incl.tax,etc)': 50.0},
        {'Category': 'BEVERAGES', 'Sub Category': 'Coffee', 'Opening Qty': 50, 'Closing Qty': 40, 'WAC(incl.tax,etc)': 80.0},
        {'Category': 'FOOD', 'Sub Category': 'Rice', 'Opening Qty': 200, 'Closing Qty': 150, 'WAC(incl.tax,etc)': 30.0},
        {'Category': 'FOOD', 'Sub Category': 'Dal', 'Opening Qty': 80, 'Closing Qty': 60, 'WAC(incl.tax,etc)': 40.0},
        {'Category': 'SNACKS', 'Sub Category': 'Chips', 'Opening Qty': 60, 'Closing Qty': 45, 'WAC(incl.tax,etc)': 25.0},
    ]
    
    # Sample purchase data
    purchase_data = [
        {'Category': 'BEVERAGES', 'Sub Category': 'Tea', 'Purchase Qty': 50, 'WAC(incl.tax,etc)': 50.0},
        {'Category': 'BEVERAGES', 'Sub Category': 'Coffee', 'Purchase Qty': 20, 'WAC(incl.tax,etc)': 80.0},
        {'Category': 'FOOD', 'Sub Category': 'Rice', 'Purchase Qty': 100, 'WAC(incl.tax,etc)': 30.0},
        {'Category': 'FOOD', 'Sub Category': 'Dal', 'Purchase Qty': 40, 'WAC(incl.tax,etc)': 40.0},
        {'Category': 'SNACKS', 'Sub Category': 'Chips', 'Purchase Qty': 30, 'WAC(incl.tax,etc)': 25.0},
    ]
    
    # Category-workarea mappings
    category_workarea_mappings = [
        {'categoryName': 'BEVERAGES', 'workAreas': ['Bar_Counter']},
        {'categoryName': 'FOOD', 'workAreas': ['Kitchen_Main']},
        {'categoryName': 'SNACKS', 'workAreas': ['Bar_Counter']},
    ]
    
    return (
        pd.DataFrame(store_data),
        pd.DataFrame(consumption_data), 
        pd.DataFrame(purchase_data),
        category_workarea_mappings
    )

def analyze_cross_category_indents(result_data):
    """Analyze and validate cross-category indent calculations"""

    print("\n" + "="*80)
    print("CROSS-CATEGORY INDENTS ANALYSIS")
    print("="*80)

    # Extract validation data
    cross_category_summary = result_data.get('cross_category_summary', {})
    cross_category_audit_trail = result_data.get('cross_category_audit_trail', [])
    validation_status = result_data.get('validation_status', 'UNKNOWN')

    # Print summary
    print(f"\nVALIDATION STATUS: {validation_status}")
    print(f"Total Outgoing: {format_indian_currency(cross_category_summary.get('total_outgoing', 0))}")
    print(f"Total Incoming: {format_indian_currency(cross_category_summary.get('total_incoming', 0))}")

    balance_diff = abs(cross_category_summary.get('total_outgoing', 0) - cross_category_summary.get('total_incoming', 0))
    print(f"Balance Difference: {format_indian_currency(balance_diff)}")

    if balance_diff > 0.01:
        print("⚠️  WARNING: Balance mismatch detected!")
    else:
        print("✅ SUCCESS: Cross-category indents are balanced!")

    # Print detailed transfers
    print(f"\nDETAILED TRANSFERS ({len(cross_category_audit_trail)} transfers):")
    print("-" * 120)
    print(f"{'#':<3} {'From Category':<15} {'From Subcategory':<15} {'To Category':<15} {'Workarea':<15} {'Original':<12} {'Distributed':<12} {'Factor':<8}")
    print("-" * 120)

    for i, transfer in enumerate(cross_category_audit_trail, 1):
        print(f"{i:<3} {transfer.get('from_category', 'N/A'):<15} {transfer.get('from_subcategory', 'N/A'):<15} "
              f"{transfer.get('to_category', 'N/A'):<15} {transfer.get('workarea', 'N/A'):<15} "
              f"{transfer.get('original_value', 0):<12.2f} {transfer.get('distributed_value', 0):<12.2f} "
              f"1/{transfer.get('distribution_factor', 1):<7}")

    # Print validation errors if any
    validation_errors = cross_category_summary.get('validation_errors', [])
    if validation_errors:
        print(f"\nVALIDATION ERRORS ({len(validation_errors)} errors):")
        print("-" * 80)
        for i, error in enumerate(validation_errors, 1):
            print(f"{i}. {error.get('error_type', 'Unknown')}: {error.get('message', 'No details')}")

    print(f"\nNOTE: In the dashboard, hover over Cross-Category Indents values in the workarea table to see detailed transfer breakdowns.")

    return validation_status == 'VALID'

def manual_calculation_verification(consumption_df, category_workarea_mappings):
    """Manually verify cross-category indent calculations"""
    
    print("\n" + "="*80)
    print("MANUAL VERIFICATION")
    print("="*80)
    
    # Build category to workarea mapping
    category_to_workareas = {}
    for mapping in category_workarea_mappings:
        category_to_workareas[mapping['categoryName']] = mapping['workAreas']
    
    print("\nCategory-Workarea Mappings:")
    for category, workareas in category_to_workareas.items():
        print(f"  {category}: {workareas}")
    
    # Calculate expected cross-category transfers
    print("\nExpected Cross-Category Transfers:")
    print("-" * 60)
    
    total_expected_outgoing = 0
    total_expected_incoming = 0
    
    for _, row in consumption_df.iterrows():
        category = row['Category']
        subcategory = row['Sub Category']
        workarea = row['WorkArea']
        indent_qty = row['WorkArea Indent']
        unit_price = row['WAC(incl.tax,etc)']
        indent_value = indent_qty * unit_price
        
        if indent_value <= 0:
            continue
            
        # Find workarea owners
        workarea_owners = []
        for cat, workareas in category_to_workareas.items():
            if workarea in workareas:
                workarea_owners.append(cat)
        
        # Check if cross-category
        cross_category_owners = [owner for owner in workarea_owners if owner != category]
        
        if cross_category_owners:
            print(f"  {category}/{subcategory} -> {workarea}: {indent_value:.2f}")
            print(f"    Giving category: {category} (-{indent_value:.2f})")
            total_expected_outgoing += indent_value
            
            distributed_value = indent_value / len(cross_category_owners)
            for owner in cross_category_owners:
                print(f"    Receiving category: {owner} (+{distributed_value:.2f})")
                total_expected_incoming += distributed_value
    
    print(f"\nExpected Totals:")
    print(f"  Outgoing: {total_expected_outgoing:.2f}")
    print(f"  Incoming: {total_expected_incoming:.2f}")
    print(f"  Difference: {abs(total_expected_outgoing - total_expected_incoming):.2f}")
    
    return total_expected_outgoing, total_expected_incoming

def main():
    parser = argparse.ArgumentParser(description='Test Cross-Category Indents Calculations')
    parser.add_argument('--use-sample', action='store_true', help='Use sample data for testing')
    parser.add_argument('--tenant-id', help='Tenant ID for real data testing')
    parser.add_argument('--start-date', help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', help='End date (YYYY-MM-DD)')
    
    args = parser.parse_args()
    
    if args.use_sample or not args.tenant_id:
        print("Using sample data for testing...")
        store_df, consumption_df, purchase_df, category_workarea_mappings = create_sample_data()
    else:
        print("Real data testing not implemented yet. Use --use-sample flag.")
        return
    
    print("Testing Cross-Category Indents Calculations...")
    print(f"Store records: {len(store_df)}")
    print(f"Consumption records: {len(consumption_df)}")
    print(f"Purchase records: {len(purchase_df)}")
    print(f"Category mappings: {len(category_workarea_mappings)}")
    
    # Run the calculation
    result = create_reconciliation_table_data(
        store_df, consumption_df, purchase_df, category_workarea_mappings
    )
    
    # Analyze results
    is_valid = analyze_cross_category_indents(result)
    
    # Manual verification
    expected_outgoing, expected_incoming = manual_calculation_verification(
        consumption_df, category_workarea_mappings
    )
    
    # Compare with actual results
    actual_summary = result.get('cross_category_summary', {})
    actual_outgoing = actual_summary.get('total_outgoing', 0)
    actual_incoming = actual_summary.get('total_incoming', 0)
    
    print(f"\n" + "="*80)
    print("COMPARISON: EXPECTED vs ACTUAL")
    print("="*80)
    print(f"Outgoing - Expected: {expected_outgoing:.2f}, Actual: {actual_outgoing:.2f}, Diff: {abs(expected_outgoing - actual_outgoing):.2f}")
    print(f"Incoming - Expected: {expected_incoming:.2f}, Actual: {actual_incoming:.2f}, Diff: {abs(expected_incoming - actual_incoming):.2f}")
    
    if abs(expected_outgoing - actual_outgoing) < 0.01 and abs(expected_incoming - actual_incoming) < 0.01:
        print("✅ SUCCESS: Calculations match expected values!")
    else:
        print("❌ ERROR: Calculations do not match expected values!")
    
    print(f"\nTest completed. Check 'cross_category_test.log' for detailed logs.")

if __name__ == "__main__":
    main()
